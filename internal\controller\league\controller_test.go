package league

import (
	"os"
	"testing"
)

func TestGenerateInviteURL(t *testing.T) {
	tests := []struct {
		name        string
		appURL      string
		inviteCode  string
		expectedURL string
	}{
		{
			name:        "Production URL",
			appURL:      "https://dinbora.com.br",
			inviteCode:  "ABCDE12345",
			expectedURL: "https://dinbora.com.br/join?code=ABCDE12345",
		},
		{
			name:        "Development URL",
			appURL:      "http://localhost:3000",
			inviteCode:  "DEV123456",
			expectedURL: "http://localhost:3000/join?code=DEV123456",
		},
		{
			name:        "Empty APP_URL uses development fallback",
			appURL:      "",
			inviteCode:  "FALLBACK123",
			expectedURL: "http://localhost:8080/join?code=FALLBACK123",
		},
		{
			name:        "Test environment URL",
			appURL:      "https://test.dinbora.com.br",
			inviteCode:  "TEST789",
			expectedURL: "https://test.dinbora.com.br/join?code=TEST789",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set the environment variable for this test
			if tt.appURL != "" {
				os.Setenv("APP_URL", tt.appURL)
			} else {
				os.Unsetenv("APP_URL")
			}

			// Call the function
			result := generateInviteURL(tt.inviteCode)

			// Check the result
			if result != tt.expectedURL {
				t.Errorf("generateInviteURL() = %v, want %v", result, tt.expectedURL)
			}
		})
	}

	// Clean up environment variable
	os.Unsetenv("APP_URL")
}

func TestInviteLeagueResponse(t *testing.T) {
	response := InviteLeagueResponse{
		InviteCode: "ABCDE12345",
		InviteURL:  "https://dinbora.com.br/join?code=ABCDE12345",
	}

	if response.InviteCode != "ABCDE12345" {
		t.Errorf("Expected InviteCode to be 'ABCDE12345', got %s", response.InviteCode)
	}

	if response.InviteURL != "https://dinbora.com.br/join?code=ABCDE12345" {
		t.Errorf("Expected InviteURL to be 'https://dinbora.com.br/join?code=ABCDE12345', got %s", response.InviteURL)
	}
}

func TestJoinLeagueEndpoint(t *testing.T) {
	t.Run("JoinLeague supports both query param and body", func(t *testing.T) {
		// This is a conceptual test to document the expected behavior
		// In practice, you would need to set up a full Echo context with mocks

		// Test case 1: Query parameter should work
		// GET /v2/leagues/join?code=ABCDE12345
		// POST /v2/leagues/join?code=ABCDE12345

		// Test case 2: JSON body should work
		// POST /v2/leagues/join with {"inviteCode": "ABCDE12345"}

		// Test case 3: Query parameter takes precedence over body
		// POST /v2/leagues/join?code=QUERY123 with {"inviteCode": "BODY456"}
		// Should use "QUERY123"

		t.Log("Single JoinLeague endpoint supports both GET and POST methods")
		t.Log("Supports both query parameter (?code=) and JSON body (inviteCode)")
		t.Log("Query parameter takes precedence over JSON body when both are present")
	})
}
